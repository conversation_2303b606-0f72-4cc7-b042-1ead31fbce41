import { useState, useEffect } from 'react';
import './App.css';
import { useChat, useDatabase } from './hooks/useApi';

function App() {
  // API hooks
  const {
    loading: chatLoading,
    error: chatError,
    chats,
    currentChat,
    messages,
    setCurrentChat,
    loadChats,
    createChat,
    loadMessages,
    sendMessage,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat
  } = useChat();

  const {
    loading: dbLoading,
    error: dbError,
    tables: dbTables,
    loadTables,
    executeQuery
  } = useDatabase();

  // Local state
  const [prompt, setPrompt] = useState('');
  const [search, setSearch] = useState('');
  const [selectedTable, setSelectedTable] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Chat options state
  const [openDropdown, setOpenDropdown] = useState(null);
  const [editingChat, setEditingChat] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [showArchived, setShowArchived] = useState(false);

  // Load initial data
  useEffect(() => {
    loadChats();
    loadTables();
  }, [loadChats, loadTables]);

  // Restore selected chat when chats are loaded
  useEffect(() => {
    const savedChatId = localStorage.getItem('selectedChatId');
    if (savedChatId && chats.length > 0 && !currentChat) {
      const savedChat = chats.find(chat => chat.id === savedChatId);
      if (savedChat) {
        setCurrentChat(savedChat);
        loadMessages(savedChatId);
      } else {
        // If saved chat doesn't exist anymore, clear the localStorage
        localStorage.removeItem('selectedChatId');
      }
    }
  }, [chats, currentChat, setCurrentChat, loadMessages]);

  // Save selected chat to localStorage whenever it changes
  useEffect(() => {
    if (currentChat?.id) {
      localStorage.setItem('selectedChatId', currentChat.id);
    } else {
      localStorage.removeItem('selectedChatId');
    }
  }, [currentChat]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.chat-options')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Handle shared chat URLs
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sharedChatId = urlParams.get('chat');

    if (sharedChatId && chats.length > 0) {
      const sharedChat = chats.find(chat => chat.id === sharedChatId);
      if (sharedChat) {
        setCurrentChat(sharedChat);
        loadMessages(sharedChatId);
        // Clear the URL parameter after loading
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, [chats, setCurrentChat, loadMessages]);

  const createNewChat = async () => {
    try {
      // Create with a simple title that will be updated on first message
      await createChat('New Chat');
      // Don't select the chat yet — wait until user sends the first prompt
      setCurrentChat(null);
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  };

  // Chat options handlers
  const handleRenameChat = (chat) => {
    setEditingChat(chat.id);
    setEditTitle(chat.title);
    setOpenDropdown(null);
  };

  const handleSaveRename = async () => {
    if (editTitle.trim() && editingChat) {
      try {
        await updateChatTitle(editingChat, editTitle.trim());
        setEditingChat(null);
        setEditTitle('');
      } catch (error) {
        console.error('Failed to rename chat:', error);
      }
    }
  };

  const handleCancelRename = () => {
    setEditingChat(null);
    setEditTitle('');
  };

  const handleDeleteChat = async (chatId) => {
    try {
      await deleteChat(chatId);
      setShowDeleteConfirm(null);
      setOpenDropdown(null);
      // Clear localStorage if deleted chat was selected
      if (currentChat?.id === chatId) {
        localStorage.removeItem('selectedChatId');
      }
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  };

  const handleArchiveChat = async (chatId) => {
    try {
      await archiveChat(chatId);
      setOpenDropdown(null);
      // Clear localStorage if archived chat was selected
      if (currentChat?.id === chatId) {
        localStorage.removeItem('selectedChatId');
      }
    } catch (error) {
      console.error('Failed to archive chat:', error);
    }
  };

  const handleUnarchiveChat = async (chatId) => {
    try {
      await unarchiveChat(chatId);
      setOpenDropdown(null);
    } catch (error) {
      console.error('Failed to unarchive chat:', error);
    }
  };

  const toggleDropdown = (chatId) => {
    setOpenDropdown(openDropdown === chatId ? null : chatId);
  };

  const handleShareChat = async (chat) => {
    try {
      const chatUrl = `${window.location.origin}${window.location.pathname}?chat=${chat.id}`;

      if (navigator.share) {
        // Use native share API if available
        await navigator.share({
          title: `Chat: ${chat.title}`,
          text: `Check out this database chat conversation`,
          url: chatUrl
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(chatUrl);
        alert('Chat link copied to clipboard!');
      }
      setOpenDropdown(null);
    } catch (error) {
      console.error('Failed to share chat:', error);
      // Fallback: just copy to clipboard
      try {
        await navigator.clipboard.writeText(`${window.location.origin}${window.location.pathname}?chat=${chat.id}`);
        alert('Chat link copied to clipboard!');
      } catch (clipboardError) {
        console.error('Failed to copy to clipboard:', clipboardError);
      }
    }
  };

  // Helper function to generate chat title from message
  const generateChatTitle = (message) => {
    const messageLower = message.toLowerCase().trim();

    // Check for greeting/welcome messages first
    const simpleGreetings = ['hi', 'hello', 'hey', 'there', 'hy', 'hii'];
    if (simpleGreetings.includes(messageLower) || messageLower === 'hi there' || messageLower === 'hello there') {
      return 'Database Assistant';
    }

    // Specific database query patterns with descriptive titles (like ChatGPT)
    const queryPatterns = [
      // User-related queries
      { pattern: /(?:show|get|list|find|select).*(?:all\s+)?users?/i, title: 'User Database Query' },
      { pattern: /users?.*(?:with|where|having|join|orders?)/i, title: 'User Data Analysis' },
      { pattern: /(?:count|total|number).*users?/i, title: 'User Count Statistics' },

      // Order-related queries
      { pattern: /(?:show|get|list|find|select).*(?:all\s+)?orders?/i, title: 'Order Database Query' },
      { pattern: /orders?.*(?:with|where|having|join|users?)/i, title: 'Order Data Analysis' },
      { pattern: /(?:total|sum|revenue).*orders?/i, title: 'Order Revenue Analysis' },

      // Product-related queries
      { pattern: /(?:show|get|list|find|select).*(?:all\s+)?products?/i, title: 'Product Database Query' },
      { pattern: /products?.*(?:with|where|having|category|price)/i, title: 'Product Catalog Analysis' },
      { pattern: /(?:top|best|popular).*products?/i, title: 'Top Products Analysis' },

      // Sales and financial queries
      { pattern: /(?:sales?|revenue|profit|income|financial)/i, title: 'Sales & Revenue Analysis' },
      { pattern: /(?:total|sum).*(?:sales?|revenue|amount)/i, title: 'Financial Summary Report' },

      // Customer analysis
      { pattern: /customers?.*(?:who|that|with|analysis|orders?)/i, title: 'Customer Behavior Analysis' },

      // Ranking and comparison queries
      { pattern: /(?:top|highest|lowest|best|worst)\s+\d+/i, title: 'Ranking & Performance Analysis' },
      { pattern: /(?:compare|comparison|vs|versus)/i, title: 'Comparative Data Analysis' },

      // Aggregation queries
      { pattern: /(?:count|total|sum|average|avg|max|min)/i, title: 'Statistical Data Analysis' },

      // Time-based queries
      { pattern: /(?:recent|latest|last|this|previous).*(?:week|month|year|day)/i, title: 'Time-based Data Query' },
      { pattern: /between.*and.*(?:date|time)/i, title: 'Date Range Analysis' },

      // Join and relationship queries
      { pattern: /(?:join|with.*(?:their|its)|relationship|connect|link)/i, title: 'Multi-table Data Query' }
    ];

    // Check specific patterns first
    for (const { pattern, title } of queryPatterns) {
      if (pattern.test(message)) {
        return title;
      }
    }

    // Fallback: Extract key business terms and create meaningful title
    const businessTermMapping = {
      'user': 'User Management Query',
      'users': 'User Management Query',
      'customer': 'Customer Data Query',
      'customers': 'Customer Data Query',
      'order': 'Order Management Query',
      'orders': 'Order Management Query',
      'product': 'Product Catalog Query',
      'products': 'Product Catalog Query',
      'sales': 'Sales Analytics Query',
      'revenue': 'Revenue Analysis Query',
      'data': 'Database Query',
      'report': 'Data Report Query',
      'analysis': 'Data Analysis Query'
    };

    // Find the most relevant business term
    const words = messageLower.split(/\s+/);
    for (const word of words) {
      if (businessTermMapping[word]) {
        return businessTermMapping[word];
      }
    }

    // Final fallback - create a descriptive title
    return 'Database Query Assistant';
  };

  const handleSend = async () => {
    if (!prompt.trim() || isSubmitting) return;

    setIsSubmitting(true);
    const userMessage = prompt.trim();
    setPrompt('');

    try {
      let chatId = currentChat?.id;
      let isNewChat = false;

      // If no chat is selected, create a new one with a generated title
      if (!chatId) {
        const generatedTitle = generateChatTitle(userMessage);
        const newChat = await createChat(generatedTitle);
        chatId = newChat.id;
        setCurrentChat(newChat);
        isNewChat = true;
      }

      // Send message to backend (includes AI response)
      await sendMessage(chatId, userMessage);

      // Update chat title based on message count and content
      const chatMessages = messages[chatId] || [];
      const userMessages = chatMessages.filter(m => m.role === 'user');

      if (!isNewChat) {
        // For existing chats, update title after first message if it's a generic/default title
        const isDefaultTitle = (title) => {
          if (!title) return true;

          // Check for various default title patterns
          const defaultPatterns = [
            /^New Chat$/,
            /^Chat \d{1,2}:\d{2}$/,
            /.*Test.*$/,
            /^Test Chat$/,
            /^Greeting.*$/,
            /^Smart.*Test$/,
            /^Final.*Test$/,
            /^Improved.*$/
          ];

          return defaultPatterns.some(pattern => pattern.test(title));
        };

        if (userMessages.length === 0 && isDefaultTitle(currentChat?.title)) {
          const generatedTitle = generateChatTitle(userMessage);
          await updateChatTitle(chatId, generatedTitle);
        }
        // Update after second message to create a more comprehensive title
        else if (userMessages.length === 1) {
          const firstMessage = userMessages[0].content;
          const firstTopic = generateChatTitle(firstMessage);
          const secondTopic = generateChatTitle(userMessage);

          // Don't combine if first topic is a greeting/welcome
          const isFirstTopicGreeting = firstTopic.includes('Assistant') || firstTopic.includes('Welcome');

          if (!isFirstTopicGreeting &&
              firstTopic.toLowerCase() !== secondTopic.toLowerCase() &&
              !firstTopic.toLowerCase().includes(secondTopic.toLowerCase()) &&
              !secondTopic.toLowerCase().includes(firstTopic.toLowerCase())) {

            // Use the more specific/descriptive title instead of combining
            const finalTitle = secondTopic.length > firstTopic.length ? secondTopic : firstTopic;
            await updateChatTitle(chatId, finalTitle);
          } else if (isFirstTopicGreeting) {
            // Replace greeting title with the actual query title
            await updateChatTitle(chatId, secondTopic);
          }
        }
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      // Restore the prompt on error
      setPrompt(userMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const displayMessages = currentChat ? messages[currentChat.id] || [] : [];

  return (
    <div className="layout">
      {/* Error Display */}
      {(chatError || dbError) && (
        <div className="error-banner">
          {chatError && <p>Chat Error: {chatError}</p>}
          {dbError && <p>Database Error: {dbError}</p>}
        </div>
      )}
      {/* Left Sidebar */}
      <aside className="sidebar left">
        <button onClick={createNewChat}>+ New Chat</button>
        <div className="chats-header">
          <h3>Chats</h3>
          <button
            className={`archive-toggle ${showArchived ? 'active' : ''}`}
            onClick={() => setShowArchived(!showArchived)}
            title={showArchived ? 'Hide archived chats' : 'Show archived chats'}
          >
            {showArchived ? '�' : '�'}
          </button>
        </div>
        <ul>
          {chats.filter(chat => showArchived ? chat.archived : !chat.archived).map(chat => (
            <li key={chat.id} className="chat-item">
              {editingChat === chat.id ? (
                <div className="chat-edit">
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveRename();
                      if (e.key === 'Escape') handleCancelRename();
                    }}
                    onBlur={handleSaveRename}
                    autoFocus
                    className="chat-edit-input"
                  />
                </div>
              ) : (
                <div className="chat-item-content">
                  <button
                    className={`chat-button ${currentChat?.id === chat.id ? 'active' : ''}`}
                    onClick={() => {
                      setCurrentChat(chat);
                      loadMessages(chat.id);
                    }}
                    title={chat.title}
                  >
                    {chat.title}
                  </button>
                  <div className="chat-options">
                    <button
                      className="options-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(chat.id);
                      }}
                      title="Chat options"
                    >
                      ⋮
                    </button>
                    {openDropdown === chat.id && (
                      <div className="dropdown-menu">
                        <button
                          onClick={() => handleRenameChat(chat)}
                          className="dropdown-item"
                        >
                          <span className="dropdown-icon">✎</span> Rename
                        </button>
                        <button
                          onClick={() => handleShareChat(chat)}
                          className="dropdown-item"
                        >
                          <span className="dropdown-icon">⤴</span> Share
                        </button>
                        {chat.archived ? (
                          <button
                            onClick={() => handleUnarchiveChat(chat.id)}
                            className="dropdown-item"
                          >
                            <span className="dropdown-icon">↗</span> Unarchive
                          </button>
                        ) : (
                          <button
                            onClick={() => handleArchiveChat(chat.id)}
                            className="dropdown-item"
                          >
                            <span className="dropdown-icon">↘</span> Archive
                          </button>
                        )}
                        <button
                          onClick={() => setShowDeleteConfirm(chat.id)}
                          className="dropdown-item delete"
                        >
                          <span className="dropdown-icon">✕</span> Delete
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Delete confirmation modal */}
              {showDeleteConfirm === chat.id && (
                <div className="delete-confirm-overlay">
                  <div className="delete-confirm-modal">
                    <h3>Delete Chat?</h3>
                    <p>This action cannot be undone. All messages in this chat will be permanently deleted.</p>
                    <div className="delete-confirm-buttons">
                      <button
                        onClick={() => setShowDeleteConfirm(null)}
                        className="cancel-button"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleDeleteChat(chat.id)}
                        className="delete-button"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </li>
          ))}
        </ul>
      </aside>

      {/* Main Area */}
      <main className="main">
        {currentChat && displayMessages.length > 0 ? (
          <>
            <div className="chat-window">
              {displayMessages.map((msg, idx) => (
                <div key={idx} className={`message ${msg.role}`}>
                  {msg.content}
                </div>
              ))}
            </div>
            <div className="prompt-bar">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="welcome-screen">
            <h2>Hello, What's on your mind?</h2>
            <div className="prompt-bar centered">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Right Sidebar */}
      <aside className="sidebar right">
        <h3>DB Tables</h3>
        <input
          type="text"
          placeholder="Search tables..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        <ul>
          {dbTables
            .filter(table => table.name.toLowerCase().includes(search.toLowerCase()))
            .map(table => (
              <li key={table.name}>
                <button
                  className={selectedTable === table.name ? 'active' : ''}
                  onClick={() =>
                    setSelectedTable(selectedTable === table.name ? null : table.name)
                  }
                >
                  {table.name}
                </button>
                {selectedTable === table.name && (
                  <ul className="columns-list">
                    {table.columns?.map(col => (
                      <li key={col.name || col}>{col.name || col}</li>
                    )) || <li>Loading columns...</li>}
                  </ul>
                )}
              </li>
            ))}
        </ul>
      </aside>
    </div>
  );
}

export default App;

import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      // You might want to redirect to login page here
    }
    return Promise.reject(error);
  }
);

// Chat API functions
export const chatAPI = {
  // Get all chats for the user
  getChats: async (includeArchived = false) => {
    const response = await api.get(`/chats?include_archived=${includeArchived}`);
    return response.data;
  },

  // Get archived chats
  getArchivedChats: async () => {
    const response = await api.get('/chats/archived');
    return response.data;
  },

  // Create a new chat
  createChat: async (title = 'New Chat') => {
    const response = await api.post('/chats', { title });
    return response.data;
  },

  // Get specific chat with messages
  getChat: async (chatId) => {
    const response = await api.get(`/chats/${chatId}`);
    return response.data;
  },

  // Update chat title
  updateChat: async (chatId, updates) => {
    const response = await api.put(`/chats/${chatId}`, updates);
    return response.data;
  },

  // Delete chat
  deleteChat: async (chatId) => {
    const response = await api.delete(`/chats/${chatId}`);
    return response.data;
  },

  // Archive chat
  archiveChat: async (chatId) => {
    const response = await api.post(`/chats/${chatId}/archive`);
    return response.data;
  },

  // Unarchive chat
  unarchiveChat: async (chatId) => {
    const response = await api.post(`/chats/${chatId}/unarchive`);
    return response.data;
  },

  // Send message and get AI response
  sendMessage: async (chatId, message) => {
    const response = await api.post(`/chats/${chatId}/messages`, {
      message,
      role: 'user'
    });
    return response.data;
  },

  // Get messages for a chat
  getMessages: async (chatId) => {
    const response = await api.get(`/chats/${chatId}/messages`);
    return response.data;
  }
};

// Database API functions
export const databaseAPI = {
  // Get all available database tables
  getTables: async () => {
    const response = await api.get('/database/tables');
    return response.data;
  },

  // Get table schema (columns, types, etc.)
  getTableSchema: async (tableName) => {
    const response = await api.get(`/database/tables/${tableName}/schema`);
    return response.data;
  },

  // Execute natural language query
  executeQuery: async (query, selectedTables = []) => {
    const response = await api.post('/database/query', {
      query,
      selected_tables: selectedTables
    });
    return response.data;
  },

  // Get sample data from a table
  getSampleData: async (tableName, limit = 10) => {
    const response = await api.get(`/database/tables/${tableName}/sample?limit=${limit}`);
    return response.data;
  }
};

// Authentication API functions
export const authAPI = {
  // Login user
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    if (response.data.access_token) {
      localStorage.setItem('authToken', response.data.access_token);
    }
    return response.data;
  },

  // Register user
  register: async (email, password, name) => {
    const response = await api.post('/auth/register', { email, password, name });
    return response.data;
  },

  // Logout user
  logout: () => {
    localStorage.removeItem('authToken');
  },

  // Get current user info
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  }
};

export default api;

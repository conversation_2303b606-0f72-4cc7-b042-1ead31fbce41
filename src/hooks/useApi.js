import { useState, useCallback } from 'react';

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const execute = useCallback(async (apiCall) => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return { loading, error, execute, reset };
};

// Specific hook for chat operations
export const useChat = () => {
  const { loading, error, execute, reset } = useApi();
  const [chats, setChats] = useState([]);
  const [currentChat, setCurrentChat] = useState(null);
  const [messages, setMessages] = useState({});

  const loadChats = useCallback(async () => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.getChats());
    if (result) {
      setChats(result);
    }
    return result;
  }, [execute]);

  const createChat = useCallback(async (title) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.createChat(title));
    if (result) {
      setChats(prev => [result, ...prev]);
    }
    return result;
  }, [execute]);

  const loadMessages = useCallback(async (chatId) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.getMessages(chatId));
    if (result) {
      setMessages(prev => ({
        ...prev,
        [chatId]: result
      }));
    }
    return result;
  }, [execute]);

  const sendMessage = useCallback(async (chatId, message) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.sendMessage(chatId, message));
    if (result) {
      // Update messages with both user message and AI response
      setMessages(prev => ({
        ...prev,
        [chatId]: [...(prev[chatId] || []), ...result.messages]
      }));
    }
    return result;
  }, [execute]);

  const updateChatTitle = useCallback(async (chatId, title) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.updateChat(chatId, { title }));
    if (result) {
      setChats(prev => prev.map(chat =>
        chat.id === chatId ? { ...chat, title } : chat
      ));
    }
    return result;
  }, [execute]);

  const deleteChat = useCallback(async (chatId) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.deleteChat(chatId));
    if (result) {
      setChats(prev => prev.filter(chat => chat.id !== chatId));
      // Clear current chat if it was deleted
      setCurrentChat(prev => prev?.id === chatId ? null : prev);
      // Clear messages for deleted chat
      setMessages(prev => {
        const newMessages = { ...prev };
        delete newMessages[chatId];
        return newMessages;
      });
    }
    return result;
  }, [execute]);

  const archiveChat = useCallback(async (chatId) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.archiveChat(chatId));
    if (result) {
      setChats(prev => prev.map(chat =>
        chat.id === chatId ? { ...chat, archived: true } : chat
      ));
      // Clear current chat if it was archived
      setCurrentChat(prev => prev?.id === chatId ? null : prev);
    }
    return result;
  }, [execute]);

  const unarchiveChat = useCallback(async (chatId) => {
    const { chatAPI } = await import('../services/api');
    const result = await execute(() => chatAPI.unarchiveChat(chatId));
    if (result) {
      setChats(prev => prev.map(chat =>
        chat.id === chatId ? { ...chat, archived: false } : chat
      ));
    }
    return result;
  }, [execute]);

  return {
    loading,
    error,
    reset,
    chats,
    currentChat,
    messages,
    setCurrentChat,
    loadChats,
    createChat,
    loadMessages,
    sendMessage,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat
  };
};

// Hook for database operations
export const useDatabase = () => {
  const { loading, error, execute, reset } = useApi();
  const [tables, setTables] = useState([]);
  const [queryResult, setQueryResult] = useState(null);

  const loadTables = useCallback(async () => {
    const { databaseAPI } = await import('../services/api');
    const result = await execute(() => databaseAPI.getTables());
    if (result) {
      setTables(result);
    }
    return result;
  }, [execute]);

  const executeQuery = useCallback(async (query, selectedTables = []) => {
    const { databaseAPI } = await import('../services/api');
    const result = await execute(() => databaseAPI.executeQuery(query, selectedTables));
    if (result) {
      setQueryResult(result);
    }
    return result;
  }, [execute]);

  const getTableSchema = useCallback(async (tableName) => {
    const { databaseAPI } = await import('../services/api');
    return await execute(() => databaseAPI.getTableSchema(tableName));
  }, [execute]);

  return {
    loading,
    error,
    reset,
    tables,
    queryResult,
    loadTables,
    executeQuery,
    getTableSchema
  };
};
